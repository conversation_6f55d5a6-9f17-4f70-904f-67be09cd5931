/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-blue: #2563eb;
    --secondary-blue: #1e40af;
    --light-blue: #dbeafe;
    --accent-blue: #3b82f6;
    --white: #ffffff;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-600: #4b5563;
    --gray-800: #1f2937;
    --gray-900: #111827;
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: var(--gray-800);
    background-color: var(--white);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--gray-200);
    z-index: 1000;
    transition: var(--transition);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo h2 {
    color: var(--primary-blue);
    font-weight: 700;
    font-size: 1.5rem;
}

.nav-logo span {
    color: var(--gray-600);
    font-size: 0.875rem;
    font-weight: 400;
}

.nav-menu {
    display: flex;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--gray-600);
    font-weight: 500;
    transition: var(--transition);
    position: relative;
}

.nav-link:hover {
    color: var(--primary-blue);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-blue);
    transition: var(--transition);
}

.nav-link:hover::after {
    width: 100%;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: var(--gray-800);
    margin: 3px 0;
    transition: var(--transition);
}

/* Hero Section */
.hero {
    padding: 120px 0 80px;
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--light-blue) 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-badge {
    display: inline-block;
    background: var(--light-blue);
    color: var(--primary-blue);
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 1rem;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: 1rem;
    color: var(--gray-900);
}

.highlight {
    color: var(--primary-blue);
}

.hero-qualities {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.quality {
    color: var(--gray-600);
    font-weight: 500;
    font-size: 1.125rem;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: var(--gray-600);
    margin-bottom: 2rem;
}

.hero-cta {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn-primary, .btn-secondary {
    padding: 0.875rem 2rem;
    border-radius: var(--border-radius);
    font-weight: 600;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: var(--transition);
    font-size: 1rem;
}

.btn-primary {
    background: var(--primary-blue);
    color: var(--white);
}

.btn-primary:hover {
    background: var(--secondary-blue);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: var(--white);
    color: var(--primary-blue);
    border: 2px solid var(--primary-blue);
}

.btn-secondary:hover {
    background: var(--primary-blue);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.hero-visual {
    display: flex;
    justify-content: center;
}

.hero-card {
    background: var(--white);
    padding: 2rem;
    border-radius: 16px;
    box-shadow: var(--shadow-lg);
    text-align: center;
    max-width: 400px;
}

.hero-card h3 {
    color: var(--gray-900);
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 2rem;
}

.stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}

.stat {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-blue);
}

.stat-label {
    font-size: 0.875rem;
    color: var(--gray-600);
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 1rem;
}

.section-header p {
    font-size: 1.125rem;
    color: var(--gray-600);
}

/* Problems Section */
.problems {
    padding: 80px 0;
    background: var(--white);
}

.problems-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.problem-card {
    background: var(--gray-50);
    padding: 2rem;
    border-radius: var(--border-radius);
    text-align: center;
    transition: var(--transition);
    border: 1px solid var(--gray-200);
}

.problem-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.problem-icon {
    width: 60px;
    height: 60px;
    background: var(--light-blue);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
}

.problem-icon i {
    font-size: 1.5rem;
    color: var(--primary-blue);
}

.problem-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 1rem;
}

.problem-card p {
    color: var(--gray-600);
    line-height: 1.6;
}

/* Solutions Section */
.solutions {
    padding: 80px 0;
    background: var(--light-blue);
}

.solutions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.solution-card {
    background: var(--white);
    padding: 2rem;
    border-radius: var(--border-radius);
    text-align: center;
    transition: var(--transition);
    box-shadow: var(--shadow);
}

.solution-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.solution-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-blue);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
}

.solution-icon i {
    font-size: 1.5rem;
    color: var(--white);
}

.solution-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 1rem;
}

.solution-card p {
    color: var(--gray-600);
    line-height: 1.6;
}

/* Features Section */
.features {
    padding: 80px 0;
    background: var(--white);
}

.features-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.features-text h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 1rem;
}

.features-text p {
    font-size: 1.125rem;
    color: var(--gray-600);
    margin-bottom: 2rem;
}

.feature-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.feature-item i {
    color: var(--primary-blue);
    font-size: 1.25rem;
    width: 20px;
}

.feature-item span {
    color: var(--gray-700);
    font-weight: 500;
}

.signup-form {
    background: var(--gray-50);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.signup-form h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 1.5rem;
    text-align: center;
}

.signup-form form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.signup-form input,
.signup-form select {
    padding: 0.875rem;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
}

.signup-form input:focus,
.signup-form select:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Differentiators Section */
.differentiators {
    padding: 80px 0;
    background: var(--gray-50);
}

.differentiators h2 {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 3rem;
}

.diff-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.diff-card {
    background: var(--white);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    transition: var(--transition);
    border-left: 4px solid var(--primary-blue);
}

.diff-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.diff-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 1rem;
}

.diff-card p {
    color: var(--gray-600);
    line-height: 1.6;
}

/* Vision Section */
.vision {
    padding: 80px 0;
    background: var(--primary-blue);
    color: var(--white);
}

.vision-content {
    text-align: center;
}

.vision-content h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.vision-subtitle {
    font-size: 1.25rem;
    opacity: 0.9;
    margin-bottom: 3rem;
}

.vision-goals {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.goal {
    background: rgba(255, 255, 255, 0.1);
    padding: 2rem;
    border-radius: var(--border-radius);
    text-align: center;
    transition: var(--transition);
    backdrop-filter: blur(10px);
}

.goal:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-3px);
}

.goal-icon {
    width: 60px;
    height: 60px;
    background: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
}

.goal-icon i {
    font-size: 1.5rem;
    color: var(--primary-blue);
}

.goal h3 {
    font-size: 1.125rem;
    font-weight: 600;
    line-height: 1.4;
}

/* Footer */
.footer {
    background: var(--gray-900);
    color: var(--white);
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 3rem;
    margin-bottom: 2rem;
}

.footer-brand h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-blue);
    margin-bottom: 0.5rem;
}

.footer-brand p {
    color: var(--gray-400);
    margin-bottom: 0.5rem;
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
}

.footer-section h4 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--white);
}

.footer-section a {
    display: block;
    color: var(--gray-400);
    text-decoration: none;
    margin-bottom: 0.5rem;
    transition: var(--transition);
}

.footer-section a:hover {
    color: var(--primary-blue);
}

.footer-bottom {
    border-top: 1px solid var(--gray-700);
    padding-top: 1rem;
    text-align: center;
    color: var(--gray-400);
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: var(--white);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: var(--shadow);
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-toggle {
        display: flex;
    }

    .nav-toggle.active .bar:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active .bar:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }

    .nav-toggle.active .bar:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }

    .hero-container {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-cta {
        justify-content: center;
    }

    .features-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .footer-links {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .section-header h2 {
        font-size: 2rem;
    }

    .features-text h2 {
        font-size: 2rem;
    }

    .vision-content h2 {
        font-size: 2rem;
    }

    .differentiators h2 {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .hero-qualities {
        justify-content: center;
    }

    .btn-primary, .btn-secondary {
        width: 100%;
        text-align: center;
    }

    .stats {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .problems-grid,
    .solutions-grid,
    .diff-grid,
    .vision-goals {
        grid-template-columns: 1fr;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hero-content,
.hero-visual,
.problem-card,
.solution-card,
.diff-card,
.goal {
    animation: fadeInUp 0.6s ease-out;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Focus styles for accessibility */
button:focus,
input:focus,
select:focus,
a:focus {
    outline: 2px solid var(--primary-blue);
    outline-offset: 2px;
}
